#pragma once

// Windows header order fix - must be before libwebsockets.h
#if defined(_WIN32)
#ifndef NOMINMAX
#define NOMINMAX
#endif
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#include <winsock2.h>
#include <ws2tcpip.h>
#include <windows.h>
#endif

#include <string>
#include <vector>
#include <functional>
#include <memory>
#include <thread>
#include <atomic>
#include <mutex>
#include <queue>
#include <chrono>
#include <condition_variable>
#include <nlohmann/json.hpp>
#include <libwebsockets.h>

// Forward declarations
struct lws_context;
struct lws;

// MIDI buffer structure (8 bytes as specified in the protocol)
struct MidiBuffer {
    uint8_t status;      // MIDI status byte (144=NoteOn, 128=NoteOff)
    uint8_t note;        // Note number (0-127)
    uint8_t velocity;    // Velocity (0-127)
    uint8_t color_r;     // Red component (0-255)
    uint8_t color_g;     // Green component (0-255)
    uint8_t color_b;     // Blue component (0-255)
    uint8_t delta_hi;    // Delta time high byte
    uint8_t delta_lo;    // Delta time low byte
    
    // Helper to set delta time (0-65535 ms)
    void setDeltaTime(uint16_t delta_ms) {
        delta_hi = (delta_ms >> 8) & 0xFF;
        delta_lo = delta_ms & 0xFF;
    }
    
    // Helper to get delta time
    uint16_t getDeltaTime() const {
        return (static_cast<uint16_t>(delta_hi) << 8) | delta_lo;
    }
    
    // Helper to set RGB color from single value
    void setColor(uint32_t rgb) {
        color_r = (rgb >> 16) & 0xFF;
        color_g = (rgb >> 8) & 0xFF;
        color_b = rgb & 0xFF;
    }
    
    // Helper to get RGB color as single value
    uint32_t getColor() const {
        return (static_cast<uint32_t>(color_r) << 16) |
               (static_cast<uint32_t>(color_g) << 8) |
               static_cast<uint32_t>(color_b);
    }
};

// User information structure
struct UserInfo {
    std::string id;
    std::string username;
    int connectedClients = 0;
};

// Message types for callbacks
enum class MessageType {
    Connected,
    Midi,
    UsernameUpdated,
    UserUpdate,
    Error,
    Pong
};

// Callback function types
using ConnectedCallback = std::function<void(const std::string& clientId, const UserInfo& user, const std::vector<UserInfo>& activeUsers)>;
using MidiCallback = std::function<void(const std::vector<MidiBuffer>& buffers, const UserInfo& fromUser)>;
using UsernameUpdatedCallback = std::function<void(const std::string& newUsername)>;
using UserUpdateCallback = std::function<void(const std::vector<UserInfo>& users)>;
using ErrorCallback = std::function<void(const std::string& message)>;
using PongCallback = std::function<void()>;
using ChatCallback = std::function<void(const std::string& message, const UserInfo& fromUser, uint64_t timestamp)>;

class WebSocketClient {
public:
    WebSocketClient();
    ~WebSocketClient();
    
    // Connection management
    bool connect(const std::string& host = "localhost", int port = 9191);
    void disconnect();
    bool isConnected() const;

    // Auto-reconnection
    void setAutoReconnect(bool enable);
    bool getAutoReconnect() const;
    
    // Message sending
    bool sendMidi(const std::vector<MidiBuffer>& buffers);
    bool sendMidi(const MidiBuffer& buffer); // Single buffer convenience method
    bool sendNoteOn(uint8_t note, uint8_t velocity, uint32_t color = 0xFFFFFF, uint16_t deltaTime = 0);
    bool sendNoteOff(uint8_t note, uint8_t velocity = 0, uint32_t color = 0xFFFFFF, uint16_t deltaTime = 0);
    bool updateUsername(const std::string& username);
    bool requestUsers();
    bool sendPing();
    bool sendChat(const std::string& message);
    
    // Callback registration
    void setConnectedCallback(ConnectedCallback callback);
    void setMidiCallback(MidiCallback callback);
    void setUsernameUpdatedCallback(UsernameUpdatedCallback callback);
    void setUserUpdateCallback(UserUpdateCallback callback);
    void setErrorCallback(ErrorCallback callback);
    void setPongCallback(PongCallback callback);
    void setChatCallback(ChatCallback callback);
    
    // User information
    const std::string& getClientId() const { return clientId_; }
    const UserInfo& getCurrentUser() const { return currentUser_; }
    const std::vector<UserInfo>& getActiveUsers() const { return activeUsers_; }
    
private:
    // LibWebSockets callbacks
    static int lwsCallback(struct lws* wsi, enum lws_callback_reasons reason,
                          void* user, void* in, size_t len);
    
    // Message handling
    void handleMessage(const std::string& message);
    void processOutgoingQueue();
    
    // Internal sending
    bool sendMessage(const nlohmann::json& message);
    void queueMessage(const nlohmann::json& message);
    bool sendMessageDirect(const std::string& message);

    // Auto-reconnection
    void attemptReconnect();
    void handleConnectionLoss(const std::string& reason);
    void cleanupAfterDisconnect(bool setShouldStop);
    
    // Connection state
    std::atomic<bool> connected_;
    std::atomic<bool> shouldStop_;
    std::atomic<bool> disconnectHandled_;  // Flag to ensure handleConnectionLoss is called only once
    
    // LibWebSockets context and connection
    struct lws_context* context_;
    struct lws* wsi_;
    
    // Threading - separate threads for different operations
    std::unique_ptr<std::thread> serviceThread_;        // WebSocket service loop
    std::unique_ptr<std::thread> messageProcessThread_; // Message processing loop
    std::mutex messageMutex_;
    std::queue<std::string> incomingMessages_;
    std::condition_variable messageCondition_;          // For waking up message processing thread

    // Outgoing message queue
    std::mutex outgoingMutex_;
    std::queue<std::string> outgoingMessages_;

    // State protection mutex
    mutable std::mutex stateMutex_;

    // Rate limiting
    std::chrono::steady_clock::time_point lastSendTime_;
    static constexpr int maxMessagesPerSecond_ = 1000; // Increased for better real-time performance
    
    // User state
    std::string clientId_;
    UserInfo currentUser_;
    std::vector<UserInfo> activeUsers_;

    // Callbacks
    ConnectedCallback connectedCallback_;
    MidiCallback midiCallback_;
    UsernameUpdatedCallback usernameUpdatedCallback_;
    UserUpdateCallback userUpdateCallback_;
    ErrorCallback errorCallback_;
    PongCallback pongCallback_;
    ChatCallback chatCallback_;

    // Auto-reconnection
    bool autoReconnect_;
    std::string lastHost_;
    int lastPort_;
    std::unique_ptr<std::thread> reconnectThread_;
    std::chrono::steady_clock::time_point lastReconnectAttempt_;
    int reconnectAttempts_;
    static constexpr int maxReconnectAttempts_ = 10;
    static constexpr std::chrono::seconds initialReconnectDelay_{2};  // Start with 2 seconds
    static constexpr int maxReconnectDelay_ = 60;  // Max 60 seconds

    // Protocol info
    static const char* protocolName_;
    static struct lws_protocols protocols_[];
};
