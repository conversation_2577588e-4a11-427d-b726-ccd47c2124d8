#include "user_indicator_manager.h"
#include <algorithm>
#include <cmath>

UserIndicatorManager::UserIndicatorManager()
    : basePosition_(4.0f, FIXED_Y_POSITION) {
}

UserIndicatorManager::~UserIndicatorManager() {
}

void UserIndicatorManager::Initialize() {
    userIndicators_.clear();
}

void UserIndicatorManager::Update() {
    for (auto& userIndicator : userIndicators_) {
        if (userIndicator.isActive) {
            // Update animation state
            if (userIndicator.animation.IsFinished()) {
                userIndicator.animation.Stop();
            }
        }
    }
}

void UserIndicatorManager::Render(OpenGLRenderer& renderer) {
    for (auto& userIndicator : userIndicators_) {
        if (userIndicator.isActive) {
            // Get current position (with animation offset)
            Vec2 currentPos = userIndicator.animation.GetCurrentPosition();

            // Render the user indicator as a colored rectangle with border
            Color borderColor(0.2f, 0.2f, 0.2f, 0.8f);
            renderer.DrawRectWithBorder(currentPos, userIndicator.size,
                                      userIndicator.userColor, borderColor, 1.0f);

            // Draw username text (if renderer supports text rendering)
            // For now, just render the colored rectangle
        }
    }
}

void UserIndicatorManager::AddUser(const UserInfo& user) {
    // Check if user already exists
    auto it = std::find_if(userIndicators_.begin(), userIndicators_.end(),
        [&user](const UserIndicator& indicator) {
            return indicator.userId == user.id;
        });
    
    if (it != userIndicators_.end()) {
        // Update existing user
        it->username = user.username;
        it->isActive = true;
        return;
    }

    // Generate a unique color for this user
    Color userColor = GenerateUserColor(user.id);
    
    // Create new user indicator
    UserIndicator newIndicator(user.id, user.username, userColor);
    userIndicators_.push_back(newIndicator);
    
    // Update positions for all indicators
    UpdateIndicatorPositions();
}

void UserIndicatorManager::RemoveUser(const std::string& userId) {
    auto it = std::find_if(userIndicators_.begin(), userIndicators_.end(),
        [&userId](const UserIndicator& indicator) {
            return indicator.userId == userId;
        });
    
    if (it != userIndicators_.end()) {
        userIndicators_.erase(it);
        UpdateIndicatorPositions();
    }
}

void UserIndicatorManager::UpdateUsers(const std::vector<UserInfo>& users) {
    // Mark all current users as inactive
    for (auto& indicator : userIndicators_) {
        indicator.isActive = false;
    }
    
    // Add or update users from the new list
    for (const auto& user : users) {
        AddUser(user);
    }
    
    // Remove inactive users
    userIndicators_.erase(
        std::remove_if(userIndicators_.begin(), userIndicators_.end(),
            [](const UserIndicator& indicator) {
                return !indicator.isActive;
            }),
        userIndicators_.end()
    );
    
    UpdateIndicatorPositions();
}

void UserIndicatorManager::ClearUsers() {
    userIndicators_.clear();
}

void UserIndicatorManager::TriggerUserAnimation(const std::string& userId) {
    auto it = std::find_if(userIndicators_.begin(), userIndicators_.end(),
        [&userId](UserIndicator& indicator) {
            return indicator.userId == userId;
        });

    if (it != userIndicators_.end()) {
        it->animation.Start(it->position);
    }
}

UserIndicator* UserIndicatorManager::GetUserIndicator(const std::string& userId) {
    auto it = std::find_if(userIndicators_.begin(), userIndicators_.end(),
        [&userId](const UserIndicator& indicator) {
            return indicator.userId == userId;
        });
    
    return (it != userIndicators_.end()) ? &(*it) : nullptr;
}

const std::vector<UserIndicator>& UserIndicatorManager::GetUserIndicators() const {
    return userIndicators_;
}

void UserIndicatorManager::SetBasePosition(const Vec2& basePos) {
    basePosition_ = Vec2(basePos.x, FIXED_Y_POSITION); // Y is always fixed
    UpdateIndicatorPositions();
}

Color UserIndicatorManager::GenerateUserColor(const std::string& userId) {
    // Generate a consistent color based on user ID hash
    std::hash<std::string> hasher;
    size_t hash = hasher(userId);
    
    // Use hash to generate HSV color, then convert to RGB
    float hue = (hash % 360) / 360.0f;
    float saturation = 0.7f + (hash % 30) / 100.0f; // 0.7 - 1.0
    float value = 0.8f + (hash % 20) / 100.0f;      // 0.8 - 1.0
    
    // Convert HSV to RGB
    float c = value * saturation;
    float x = c * (1.0f - std::abs(std::fmod(hue * 6.0f, 2.0f) - 1.0f));
    float m = value - c;
    
    float r, g, b;
    if (hue < 1.0f/6.0f) {
        r = c; g = x; b = 0;
    } else if (hue < 2.0f/6.0f) {
        r = x; g = c; b = 0;
    } else if (hue < 3.0f/6.0f) {
        r = 0; g = c; b = x;
    } else if (hue < 4.0f/6.0f) {
        r = 0; g = x; b = c;
    } else if (hue < 5.0f/6.0f) {
        r = x; g = 0; b = c;
    } else {
        r = c; g = 0; b = x;
    }
    
    return Color(r + m, g + m, b + m, 1.0f);
}

void UserIndicatorManager::UpdateIndicatorPositions() {
    for (size_t i = 0; i < userIndicators_.size(); ++i) {
        float x = basePosition_.x + i * (INDICATOR_WIDTH + INDICATOR_SPACING);
        Vec2 position(x, FIXED_Y_POSITION);
        userIndicators_[i].position = position;
        userIndicators_[i].animation.originalPosition = position;
    }
}
