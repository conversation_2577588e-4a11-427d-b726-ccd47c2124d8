// Windows header order fix - must be before any other Windows headers
#if defined(_WIN32)
#ifndef NOMINMAX
#define NOMINMAX
#endif
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#include <winsock2.h>
#include <ws2tcpip.h>
#include <windows.h>
#endif

#include "websocket_client.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <chrono>
#include <cstring>

// Static protocol definition
const char* WebSocketClient::protocolName_ = "piano-protocol";

struct lws_protocols WebSocketClient::protocols_[] = {
    {
        WebSocketClient::protocolName_,
        WebSocketClient::lwsCallback,
        0,
        4096,
        0, nullptr, 0
    },
    { nullptr, nullptr, 0, 0, 0, nullptr, 0 } // terminator
};

WebSocketClient::WebSocketClient()
    : connected_(false)
    , shouldStop_(false)
    , disconnectHandled_(false)
    , context_(nullptr)
    , wsi_(nullptr)
    , lastSendTime_(std::chrono::steady_clock::now())
    , autoReconnect_(false)
    , lastPort_(0)
    , reconnectAttempts_(0)
{
}

WebSocketClient::~WebSocketClient() {
    autoReconnect_ = false;
    disconnect();

    // Wait for reconnect thread to finish
    if (reconnectThread_ && reconnectThread_->joinable()) {
        reconnectThread_->join();
    }
}

bool WebSocketClient::connect(const std::string& host, int port) {
    std::cerr << "WebSocketClient::connect() called with host=" << host << ", port=" << port << std::endl;

    // Store connection info for auto-reconnect
    lastHost_ = host;
    lastPort_ = port;
    reconnectAttempts_ = 0;

    // Always ensure clean state before connecting
    std::cerr << "Ensuring clean state before connecting..." << std::endl;

    // Set stop flag to ensure any existing operations stop
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        shouldStop_ = true;
        connected_ = false;
    }

    // Wake up any waiting threads
    messageCondition_.notify_all();

    // Clean up any existing resources
    if (serviceThread_) {
        if (serviceThread_->joinable()) {
            std::cerr << "Cleaning up existing service thread..." << std::endl;
            serviceThread_->join();
        }
        serviceThread_.reset();
    }

    if (messageProcessThread_) {
        if (messageProcessThread_->joinable()) {
            std::cerr << "Cleaning up existing message thread..." << std::endl;
            messageProcessThread_->join();
        }
        messageProcessThread_.reset();
    }

    if (context_) {
        std::cerr << "Cleaning up existing context..." << std::endl;
        lws_context_destroy(context_);
        context_ = nullptr;
    }

    wsi_ = nullptr;
    std::cerr << "Clean state achieved" << std::endl;

    // Reset flags for new connection
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        shouldStop_ = false;
        disconnectHandled_ = false;
        std::cerr << "Reset shouldStop_ and disconnectHandled_ flags to false" << std::endl;
    }


    
    // Create LWS context
    struct lws_context_creation_info info;
    memset(&info, 0, sizeof(info));
    
    info.port = CONTEXT_PORT_NO_LISTEN;
    info.protocols = protocols_;
    info.gid = -1;
    info.uid = -1;
    info.user = this;
    
    context_ = lws_create_context(&info);
    if (!context_) {
        return false;
    }
    
    // Create connection
    struct lws_client_connect_info ccinfo;
    memset(&ccinfo, 0, sizeof(ccinfo));
    
    ccinfo.context = context_;
    ccinfo.address = host.c_str();
    ccinfo.port = port;
    ccinfo.path = "/";
    ccinfo.host = ccinfo.address;
    ccinfo.origin = ccinfo.address;
    ccinfo.protocol = protocolName_;
    ccinfo.userdata = this;
    
    wsi_ = lws_client_connect_via_info(&ccinfo);
    if (!wsi_) {
        lws_context_destroy(context_);
        context_ = nullptr;
        return false;
    }

    // Start WebSocket service thread (handles network I/O only)
    std::cerr << "Creating service thread..." << std::endl;
    serviceThread_ = std::make_unique<std::thread>([this]() {
        std::cerr << "Service thread started" << std::endl;
        while (!shouldStop_) {
            // Process WebSocket events with minimal timeout
            lws_service(context_, 1); // 1ms timeout for network I/O

            // Short sleep to prevent excessive CPU usage
            std::this_thread::sleep_for(std::chrono::microseconds(100));
        }
        std::cerr << "Service thread ending" << std::endl;
    });

    // Start message processing thread (handles incoming message processing)
    std::cerr << "Creating message processing thread..." << std::endl;
    messageProcessThread_ = std::make_unique<std::thread>([this]() {
        std::cerr << "Message processing thread started" << std::endl;
        while (!shouldStop_) {
            std::unique_lock<std::mutex> lock(messageMutex_);

            // Wait for messages or stop signal
            messageCondition_.wait(lock, [this] {
                return !incomingMessages_.empty() || shouldStop_;
            });

            // Process all available messages
            while (!incomingMessages_.empty() && !shouldStop_) {
                std::string message = incomingMessages_.front();
                incomingMessages_.pop();

                // Release lock while processing message
                lock.unlock();
                handleMessage(message);
                lock.lock();
            }
        }
        std::cerr << "Message processing thread ending" << std::endl;
    });

    std::cerr << "WebSocketClient::connect() completed successfully" << std::endl;
    return true;
}

void WebSocketClient::disconnect() {
    std::cerr << "WebSocketClient::disconnect() called" << std::endl;

    // Check if already disconnected
    bool alreadyDisconnected = false;
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        alreadyDisconnected = shouldStop_ && !connected_ && !context_ && !wsi_;

        // Set stop flag first to prevent new operations
        shouldStop_ = true;
        connected_ = false;
        // Disable auto-reconnect for manual disconnection
        autoReconnect_ = false;
        std::cerr << "Set shouldStop_ = true, connected_ = false, autoReconnect_ = false" << std::endl;
    }

    if (alreadyDisconnected) {
        std::cerr << "Already disconnected, skipping cleanup" << std::endl;
        return;
    }

    // Wake up message processing thread first
    std::cerr << "Notifying message processing thread..." << std::endl;
    messageCondition_.notify_all();

    // Close WebSocket connection properly (only if still valid)
    bool hasValidConnection = false;
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        hasValidConnection = (wsi_ != nullptr && context_ != nullptr);
        if (hasValidConnection) {
            std::cerr << "Requesting connection close..." << std::endl;
            // Request connection close
            lws_close_reason(wsi_, LWS_CLOSE_STATUS_NORMAL, nullptr, 0);
            // Don't set wsi_ to nullptr here - let the callback handle it
        } else {
            std::cerr << "Connection already closed, skipping close request" << std::endl;
            wsi_ = nullptr; // Ensure it's null
        }
    }

    // Wake up service thread to process the close (only if context is valid)
    if (hasValidConnection && context_) {
        std::cerr << "Cancelling service..." << std::endl;
        lws_cancel_service(context_);
    }

    // Wait for both threads to finish
    std::cerr << "Waiting for threads to finish..." << std::endl;
    if (serviceThread_) {
        if (serviceThread_->joinable()) {
            std::cerr << "Joining service thread..." << std::endl;
            serviceThread_->join();
            std::cerr << "Service thread joined" << std::endl;
        } else {
            std::cerr << "Service thread not joinable" << std::endl;
        }
        serviceThread_.reset();
        std::cerr << "Service thread reset" << std::endl;
    }

    if (messageProcessThread_) {
        if (messageProcessThread_->joinable()) {
            std::cerr << "Joining message process thread..." << std::endl;
            messageProcessThread_->join();
            std::cerr << "Message process thread joined" << std::endl;
        } else {
            std::cerr << "Message process thread not joinable" << std::endl;
        }
        messageProcessThread_.reset();
        std::cerr << "Message process thread reset" << std::endl;
    }

    // Clean up context
    std::cerr << "Cleaning up context..." << std::endl;
    if (context_) {
        lws_context_destroy(context_);
        context_ = nullptr;
        std::cerr << "Context destroyed and reset" << std::endl;
    }

    // Clear message queues
    {
        std::lock_guard<std::mutex> lock(messageMutex_);
        while (!incomingMessages_.empty()) {
            incomingMessages_.pop();
        }
    }

    {
        std::lock_guard<std::mutex> lock(outgoingMutex_);
        while (!outgoingMessages_.empty()) {
            outgoingMessages_.pop();
        }
    }

    // Wait for reconnect thread to finish
    if (reconnectThread_ && reconnectThread_->joinable()) {
        std::cerr << "Joining reconnect thread..." << std::endl;
        reconnectThread_->join();
        reconnectThread_.reset();
        std::cerr << "Reconnect thread joined and reset" << std::endl;
    }

    std::cerr << "WebSocketClient::disconnect() completed" << std::endl;
}

bool WebSocketClient::isConnected() const {
    return connected_;
}

bool WebSocketClient::sendMidi(const std::vector<MidiBuffer>& buffers) {
    if (!connected_ || buffers.empty()) {
        return false;
    }

    // Limit the number of buffers per message to prevent large messages
    // Increased from 10 to 50 for better chord/simultaneous note handling
    const size_t maxBuffersPerMessage = 50;

    // Split large buffer arrays into smaller chunks
    for (size_t i = 0; i < buffers.size(); i += maxBuffersPerMessage) {
        size_t end = std::min(i + maxBuffersPerMessage, buffers.size());
        std::vector<MidiBuffer> chunk(buffers.begin() + i, buffers.begin() + end);

        nlohmann::json message;
        message["type"] = "midi";

        // Convert MidiBuffer structs to arrays
        nlohmann::json buffersArray = nlohmann::json::array();
        for (const auto& buffer : chunk) {
            nlohmann::json bufferArray = nlohmann::json::array();
            bufferArray.push_back(buffer.status);
            bufferArray.push_back(buffer.note);
            bufferArray.push_back(buffer.velocity);
            bufferArray.push_back(buffer.color_r);
            bufferArray.push_back(buffer.color_g);
            bufferArray.push_back(buffer.color_b);
            bufferArray.push_back(buffer.delta_hi);
            bufferArray.push_back(buffer.delta_lo);
            buffersArray.push_back(bufferArray);
        }

        message["buffers"] = buffersArray;

        // Queue each chunk separately
        queueMessage(message);
    }

    return true;
}

bool WebSocketClient::sendMidi(const MidiBuffer& buffer) {
    return sendMidi(std::vector<MidiBuffer>{buffer});
}

bool WebSocketClient::sendNoteOn(uint8_t note, uint8_t velocity, uint32_t color, uint16_t deltaTime) {
    MidiBuffer buffer;
    buffer.status = 144; // Note On
    buffer.note = note;
    buffer.velocity = velocity;
    buffer.setColor(color);
    buffer.setDeltaTime(deltaTime);
    
    return sendMidi(buffer);
}

bool WebSocketClient::sendNoteOff(uint8_t note, uint8_t velocity, uint32_t color, uint16_t deltaTime) {
    MidiBuffer buffer;
    buffer.status = 128; // Note Off
    buffer.note = note;
    buffer.velocity = velocity;
    buffer.setColor(color);
    buffer.setDeltaTime(deltaTime);
    
    return sendMidi(buffer);
}

bool WebSocketClient::updateUsername(const std::string& username) {
    if (!connected_) {
        return false;
    }

    nlohmann::json message;
    message["type"] = "set_username";
    message["username"] = username;

    // Update local user info
    currentUser_.username = username;

    return sendMessage(message);
}

bool WebSocketClient::requestUsers() {
    if (!connected_) {
        return false;
    }
    
    nlohmann::json message;
    message["type"] = "get_users";
    
    return sendMessage(message);
}

bool WebSocketClient::sendPing() {
    if (!connected_) {
        return false;
    }

    nlohmann::json message;
    message["type"] = "ping";

    return sendMessage(message);
}

bool WebSocketClient::sendChat(const std::string& message) {
    if (!connected_) {
        return false;
    }

    nlohmann::json chatMessage;
    chatMessage["type"] = "chat";
    chatMessage["message"] = message;

    return sendMessage(chatMessage);
}

// Callback setters
void WebSocketClient::setConnectedCallback(ConnectedCallback callback) {
    connectedCallback_ = callback;
}

void WebSocketClient::setMidiCallback(MidiCallback callback) {
    midiCallback_ = callback;
}

void WebSocketClient::setUsernameUpdatedCallback(UsernameUpdatedCallback callback) {
    usernameUpdatedCallback_ = callback;
}

void WebSocketClient::setUserUpdateCallback(UserUpdateCallback callback) {
    userUpdateCallback_ = callback;
}

void WebSocketClient::setErrorCallback(ErrorCallback callback) {
    errorCallback_ = callback;
}

void WebSocketClient::setPongCallback(PongCallback callback) {
    pongCallback_ = callback;
}

void WebSocketClient::setChatCallback(ChatCallback callback) {
    chatCallback_ = callback;
}

bool WebSocketClient::sendMessage(const nlohmann::json& message) {
    // Queue the message for thread-safe sending
    queueMessage(message);
    return true;
}

void WebSocketClient::queueMessage(const nlohmann::json& message) {
    std::lock_guard<std::mutex> lock(outgoingMutex_);

    // Limit queue size to prevent memory issues
    // Increased from 1000 to 5000 for better handling of rapid note sequences
    const size_t maxQueueSize = 5000;
    if (outgoingMessages_.size() >= maxQueueSize) {
        // Drop oldest messages if queue is full
        while (outgoingMessages_.size() >= maxQueueSize) {
            outgoingMessages_.pop();
        }
    }

    outgoingMessages_.push(message.dump());

    // Request writable callback to process the queue
    if (connected_ && wsi_) {
        lws_callback_on_writable(wsi_);
        if (context_) {
            lws_cancel_service(context_);
        }
    }
}

bool WebSocketClient::sendMessageDirect(const std::string& messageStr) {
    if (!connected_ || !wsi_) {
        return false;
    }

    size_t len = messageStr.length();

    // Check if message is too large
    if (len > 32768) { // 32KB limit
        // Split large messages or reject them
        return false;
    }

    // Allocate buffer with LWS_PRE padding
    std::vector<unsigned char> buffer(LWS_PRE + len);
    memcpy(&buffer[LWS_PRE], messageStr.c_str(), len);

    int result = lws_write(wsi_, &buffer[LWS_PRE], len, LWS_WRITE_TEXT);

    return result >= 0;
}



void WebSocketClient::processOutgoingQueue() {
    std::lock_guard<std::mutex> lock(outgoingMutex_);

    auto now = std::chrono::steady_clock::now();
    auto timeSinceLastSend = std::chrono::duration_cast<std::chrono::microseconds>(now - lastSendTime_);

    // Reduced rate limiting for better real-time performance
    // Allow more frequent sends for immediate response
    if (timeSinceLastSend.count() < 500) { // 500 microseconds = 0.5ms
        return;
    }

    // Process messages in very small batches to not block incoming message processing
    int processed = 0;
    const int maxPerCall = 5; // Small batch size to prioritize incoming messages

    while (!outgoingMessages_.empty() && processed < maxPerCall) {
        std::string message = outgoingMessages_.front();
        outgoingMessages_.pop();

        // Release lock temporarily to send message
        outgoingMutex_.unlock();
        bool success = sendMessageDirect(message);
        outgoingMutex_.lock();

        if (success) {
            lastSendTime_ = std::chrono::steady_clock::now();
            processed++;

            // No additional delay - let WebSocket handle the flow control
        } else {
            // If send failed, put message back at front of queue
            std::queue<std::string> temp;
            temp.push(message);
            while (!outgoingMessages_.empty()) {
                temp.push(outgoingMessages_.front());
                outgoingMessages_.pop();
            }
            outgoingMessages_ = temp;
            break;
        }
    }
}

void WebSocketClient::handleMessage(const std::string& message) {
    // Check if we should stop processing (client is being destroyed)
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        if (shouldStop_) {
            return;
        }
    }

    try {
        nlohmann::json data = nlohmann::json::parse(message);

        if (!data.contains("type")) {
            return;
        }

        std::string type = data["type"];

        if (type == "connected") {
            if (data.contains("clientId") && data.contains("user")) {
                clientId_ = data["clientId"];

                auto userObj = data["user"];
                currentUser_.id = userObj["id"];
                currentUser_.username = userObj["username"];

                // Parse active users
                activeUsers_.clear();
                if (data.contains("activeUsers")) {
                    for (const auto& userJson : data["activeUsers"]) {
                        UserInfo user;
                        user.id = userJson["id"];
                        user.username = userJson["username"];
                        user.connectedClients = userJson.value("connectedClients", 1);
                        activeUsers_.push_back(user);
                    }
                }

                {
                    std::lock_guard<std::mutex> lock(stateMutex_);
                    if (connectedCallback_ && !shouldStop_) {
                        connectedCallback_(clientId_, currentUser_, activeUsers_);
                    }
                }
            }
        }
        else if (type == "midi") {
            if (data.contains("buffers") && data.contains("fromUser")) {
                std::vector<MidiBuffer> buffers;

                for (const auto& bufferJson : data["buffers"]) {
                    if (bufferJson.size() >= 8) {
                        MidiBuffer buffer;
                        buffer.status = bufferJson[0];
                        buffer.note = bufferJson[1];
                        buffer.velocity = bufferJson[2];
                        buffer.color_r = bufferJson[3];
                        buffer.color_g = bufferJson[4];
                        buffer.color_b = bufferJson[5];
                        buffer.delta_hi = bufferJson[6];
                        buffer.delta_lo = bufferJson[7];
                        buffers.push_back(buffer);
                    }
                }

                UserInfo fromUser;
                auto fromUserObj = data["fromUser"];
                fromUser.id = fromUserObj["id"];
                fromUser.username = fromUserObj["username"];

                {
                    std::lock_guard<std::mutex> lock(stateMutex_);
                    if (midiCallback_ && !buffers.empty() && !shouldStop_) {
                        midiCallback_(buffers, fromUser);
                    }
                }
            }
        }
        else if (type == "username_updated") {
            if (data.contains("username")) {
                std::string newUsername = data["username"];
                currentUser_.username = newUsername;

                if (usernameUpdatedCallback_ && !shouldStop_) {
                    usernameUpdatedCallback_(newUsername);
                }
            }
        }
        else if (type == "user_update") {
            if (data.contains("users")) {
                activeUsers_.clear();
                for (const auto& userJson : data["users"]) {
                    UserInfo user;
                    user.id = userJson["id"];
                    user.username = userJson["username"];
                    user.connectedClients = userJson.value("connectedClients", 1);
                    activeUsers_.push_back(user);
                }

                if (userUpdateCallback_ && !shouldStop_) {
                    userUpdateCallback_(activeUsers_);
                }
            }
        }
        else if (type == "error") {
            if (data.contains("message")) {
                std::string errorMessage = data["message"];

                if (errorCallback_ && !shouldStop_) {
                    errorCallback_(errorMessage);
                }
            }
        }
        else if (type == "pong") {
            if (pongCallback_ && !shouldStop_) {
                pongCallback_();
            }
        }
        else if (type == "chat") {
            if (data.contains("message") && data.contains("fromUser") && data.contains("timestamp")) {
                std::string message = data["message"];
                uint64_t timestamp = data["timestamp"];

                auto userObj = data["fromUser"];
                UserInfo fromUser;
                fromUser.id = userObj["id"];
                fromUser.username = userObj["username"];

                if (chatCallback_ && !shouldStop_) {
                    chatCallback_(message, fromUser, timestamp);
                }
            }
        }
        else if (type == "chat_history") {
            if (data.contains("messages")) {
                auto messages = data["messages"];
                for (const auto& msgData : messages) {
                    if (msgData.contains("message") && msgData.contains("fromUser") && msgData.contains("timestamp")) {
                        std::string message = msgData["message"];
                        uint64_t timestamp = msgData["timestamp"];

                        auto userObj = msgData["fromUser"];
                        UserInfo fromUser;
                        fromUser.id = userObj["id"];
                        fromUser.username = userObj["username"];

                        if (chatCallback_ && !shouldStop_) {
                            chatCallback_(message, fromUser, timestamp);
                        }
                    }
                }
            }
        }
    }
    catch (const std::exception& e) {
        if (errorCallback_ && !shouldStop_) {
            errorCallback_("Failed to parse message: " + std::string(e.what()));
        }
    }
}

int WebSocketClient::lwsCallback(struct lws* wsi, enum lws_callback_reasons reason,
                                void* user, void* in, size_t len) {
    // Get context first and check if it's valid
    struct lws_context* context = lws_get_context(wsi);
    if (!context) {
        std::cerr << "lwsCallback: Invalid context" << std::endl;
        return 0;
    }

    WebSocketClient* client = static_cast<WebSocketClient*>(lws_context_user(context));

    // Safety check: ensure client is valid and not being destroyed
    if (!client) {
        std::cerr << "lwsCallback: Invalid client pointer" << std::endl;
        return 0;
    }

    // Additional safety check with mutex
    {
        std::lock_guard<std::mutex> lock(client->stateMutex_);
        if (client->shouldStop_) {
            std::cerr << "lwsCallback: Client is stopping, ignoring callback reason " << reason << std::endl;
            return 0;
        }
    }

    switch (reason) {
        case LWS_CALLBACK_CLIENT_ESTABLISHED:
            {
                std::cerr << "lwsCallback: CLIENT_ESTABLISHED" << std::endl;
                std::lock_guard<std::mutex> lock(client->stateMutex_);
                if (!client->shouldStop_) {
                    client->connected_ = true;
                    std::cerr << "lwsCallback: Connection established successfully, connected_=true" << std::endl;
                } else {
                    std::cerr << "lwsCallback: Connection established but shouldStop_=true, not setting connected_" << std::endl;
                }
            }
            break;

        case LWS_CALLBACK_CLIENT_RECEIVE:
            if (client && !client->shouldStop_ && in && len > 0) {
                std::string message(static_cast<char*>(in), len);

                {
                    std::lock_guard<std::mutex> lock(client->messageMutex_);

                    // Don't process messages if stopping
                    if (client->shouldStop_) {
                        break;
                    }

                    // Increased queue size for better buffering
                    const size_t maxIncomingQueueSize = 100;
                    if (client->incomingMessages_.size() >= maxIncomingQueueSize) {
                        // Remove oldest messages if queue is full
                        while (client->incomingMessages_.size() >= maxIncomingQueueSize) {
                            client->incomingMessages_.pop();
                        }
                    }

                    client->incomingMessages_.push(message);
                }

                // Wake up message processing thread immediately
                if (!client->shouldStop_) {
                    client->messageCondition_.notify_one();
                }
            }
            break;

        case LWS_CALLBACK_CLIENT_CONNECTION_ERROR:
            {
                std::cerr << "lwsCallback: CLIENT_CONNECTION_ERROR" << std::endl;

                // Check connection state for debugging
                bool wasConnected = false;
                bool isManualDisconnect = false;
                bool alreadyHandled = false;
                {
                    std::lock_guard<std::mutex> lock(client->stateMutex_);
                    wasConnected = client->connected_;
                    isManualDisconnect = client->shouldStop_;
                    alreadyHandled = client->disconnectHandled_;
                    std::cerr << "lwsCallback: wasConnected=" << wasConnected << ", isManualDisconnect=" << isManualDisconnect << ", alreadyHandled=" << alreadyHandled << std::endl;

                    // Mark as handled to prevent duplicate processing
                    if (!isManualDisconnect && !alreadyHandled) {
                        client->disconnectHandled_ = true;
                        client->connected_ = false;
                        client->wsi_ = nullptr;
                    }
                }

                if (!isManualDisconnect && !alreadyHandled) {
                    std::cerr << "lwsCallback: Calling handleConnectionLoss for connection error" << std::endl;
                    client->handleConnectionLoss("Connection error");
                } else {
                    std::cerr << "lwsCallback: Skipping handleConnectionLoss (manual disconnect or already handled)" << std::endl;
                }
            }
            break;

        case LWS_CALLBACK_CLOSED:
            {
                std::cerr << "lwsCallback: CLOSED" << std::endl;

                // Check if this is an unexpected disconnection (server-side close)
                bool wasConnected = false;
                bool isManualDisconnect = false;
                bool alreadyHandled = false;
                {
                    std::lock_guard<std::mutex> lock(client->stateMutex_);
                    wasConnected = client->connected_;
                    isManualDisconnect = client->shouldStop_;
                    alreadyHandled = client->disconnectHandled_;
                    std::cerr << "lwsCallback: wasConnected=" << wasConnected << ", isManualDisconnect=" << isManualDisconnect << ", alreadyHandled=" << alreadyHandled << std::endl;

                    // Mark as handled to prevent duplicate processing
                    if (!isManualDisconnect && !alreadyHandled) {
                        client->disconnectHandled_ = true;
                        client->connected_ = false;
                        client->wsi_ = nullptr;
                    }
                }

                // If we were connected but this wasn't a manual disconnect, it's a server disconnect
                if (wasConnected && !isManualDisconnect && !alreadyHandled) {
                    std::cerr << "lwsCallback: Unexpected server disconnection detected" << std::endl;
                    client->handleConnectionLoss("Server disconnected");
                } else {
                    std::cerr << "lwsCallback: Manual disconnection or already disconnected/handled" << std::endl;
                }
            }
            break;

        case LWS_CALLBACK_WSI_DESTROY:
            {
                std::cerr << "lwsCallback: WSI_DESTROY" << std::endl;

                // Check if this is an unexpected disconnection
                bool wasConnected = false;
                bool isManualDisconnect = false;
                bool alreadyHandled = false;
                {
                    std::lock_guard<std::mutex> lock(client->stateMutex_);
                    wasConnected = client->connected_;
                    isManualDisconnect = client->shouldStop_;
                    alreadyHandled = client->disconnectHandled_;
                    std::cerr << "lwsCallback: WSI_DESTROY - wasConnected=" << wasConnected << ", isManualDisconnect=" << isManualDisconnect << ", alreadyHandled=" << alreadyHandled << std::endl;

                    // Mark as handled to prevent duplicate processing
                    if (!isManualDisconnect && !alreadyHandled) {
                        client->disconnectHandled_ = true;
                        client->connected_ = false;
                        client->wsi_ = nullptr;
                    }
                }

                // If we were connected but this wasn't a manual disconnect, it's a server disconnect
                if (wasConnected && !isManualDisconnect && !alreadyHandled) {
                    std::cerr << "lwsCallback: Server disconnection detected via WSI_DESTROY" << std::endl;
                    client->handleConnectionLoss("Server disconnected");
                } else {
                    std::cerr << "lwsCallback: WSI_DESTROY - Manual disconnection or already disconnected/handled" << std::endl;
                }
            }
            break;

        case LWS_CALLBACK_CLIENT_WRITEABLE:
            // Process outgoing message queue when socket is writable
            if (client && !client->shouldStop_) {
                client->processOutgoingQueue();

                // Request callback again if there are more messages
                {
                    std::lock_guard<std::mutex> lock(client->outgoingMutex_);
                    if (!client->outgoingMessages_.empty()) {
                        lws_callback_on_writable(wsi);
                    }
                }
            }
            break;

        case LWS_CALLBACK_CLIENT_CLOSED:
            {
                std::cerr << "lwsCallback: CLIENT_CLOSED" << std::endl;

                // Check if this is an unexpected disconnection
                bool wasConnected = false;
                bool isManualDisconnect = false;
                {
                    std::lock_guard<std::mutex> lock(client->stateMutex_);
                    wasConnected = client->connected_;
                    isManualDisconnect = client->shouldStop_;
                    std::cerr << "lwsCallback: CLIENT_CLOSED - wasConnected=" << wasConnected << ", isManualDisconnect=" << isManualDisconnect << std::endl;
                    client->connected_ = false;
                    client->wsi_ = nullptr;
                }

                // If we were connected but this wasn't a manual disconnect, it's a server disconnect
                if (wasConnected && !isManualDisconnect) {
                    std::cerr << "lwsCallback: Server disconnection detected via CLIENT_CLOSED" << std::endl;
                    client->handleConnectionLoss("Server disconnected");
                } else {
                    std::cerr << "lwsCallback: CLIENT_CLOSED - Manual disconnection or already disconnected" << std::endl;
                }
            }
            break;

        default:
            // Log all other callbacks for debugging
            if (reason != LWS_CALLBACK_GET_THREAD_ID &&
                reason != LWS_CALLBACK_LOCK_POLL &&
                reason != LWS_CALLBACK_UNLOCK_POLL) {
                std::cerr << "lwsCallback: Unhandled callback reason " << reason << std::endl;
            }
            break;
    }

    return 0;
}

// Auto-reconnection methods
void WebSocketClient::setAutoReconnect(bool enable) {
    autoReconnect_ = enable;
}

bool WebSocketClient::getAutoReconnect() const {
    return autoReconnect_;
}

void WebSocketClient::handleConnectionLoss(const std::string& reason) {
    std::cerr << "Connection lost: " << reason << std::endl;

    // Check if this is a manual disconnection
    bool isManualDisconnect = false;
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        isManualDisconnect = shouldStop_;
    }

    // Don't attempt reconnection if this was a manual disconnect
    if (isManualDisconnect) {
        std::cerr << "Manual disconnection detected, skipping reconnection" << std::endl;
        return;
    }

    std::cerr << "Server disconnection detected" << std::endl;

    // Call error callback to update UI (same as manual disconnect)
    if (errorCallback_) {
        std::cerr << "Calling error callback for server disconnect" << std::endl;
        errorCallback_(reason);
    }

    // Perform the same cleanup as manual disconnect, but in a separate thread
    // to avoid deadlock (since we're being called from the service thread)
    std::thread([this, reason]() {
        std::cerr << "Performing disconnect cleanup in separate thread..." << std::endl;

        // Wait a moment to ensure the callback has finished
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        // Perform cleanup for server disconnect (but don't set shouldStop_)
        {
            std::lock_guard<std::mutex> lock(stateMutex_);
            // Don't set shouldStop_ = true for server disconnects, only for manual disconnects
            connected_ = false;
            wsi_ = nullptr;
        }

        // Wake up message processing thread
        messageCondition_.notify_all();

        // Wait for threads to finish (safe to do from separate thread)
        if (serviceThread_ && serviceThread_->joinable()) {
            std::cerr << "Joining service thread after server disconnect..." << std::endl;
            serviceThread_->join();
            serviceThread_.reset();
        }

        if (messageProcessThread_ && messageProcessThread_->joinable()) {
            std::cerr << "Joining message thread after server disconnect..." << std::endl;
            messageProcessThread_->join();
            messageProcessThread_.reset();
        }

        // Clean up context
        if (context_) {
            std::cerr << "Destroying context after server disconnect..." << std::endl;
            lws_context_destroy(context_);
            context_ = nullptr;
        }

        // Clear user state
        clientId_.clear();
        currentUser_ = UserInfo{};
        activeUsers_.clear();

        // Clear message queues
        {
            std::lock_guard<std::mutex> lock(messageMutex_);
            while (!incomingMessages_.empty()) {
                incomingMessages_.pop();
            }
        }

        {
            std::lock_guard<std::mutex> lock(outgoingMutex_);
            while (!outgoingMessages_.empty()) {
                outgoingMessages_.pop();
            }
        }

        std::cerr << "Server disconnect cleanup completed" << std::endl;

        // Attempt reconnection if enabled
        if (autoReconnect_ && !lastHost_.empty()) {
            std::cerr << "Attempting auto-reconnection..." << std::endl;

            // Reset shouldStop_ flag for reconnection
            {
                std::lock_guard<std::mutex> lock(stateMutex_);
                shouldStop_ = false;
                disconnectHandled_ = false;
                std::cerr << "Reset shouldStop_ and disconnectHandled_ for reconnection" << std::endl;
            }

            attemptReconnect();
        }
    }).detach();
}

void WebSocketClient::attemptReconnect() {
    std::cerr << "attemptReconnect() called" << std::endl;

    if (shouldStop_) {
        std::cerr << "attemptReconnect: shouldStop_ is true, aborting" << std::endl;
        return;
    }

    if (!autoReconnect_) {
        std::cerr << "attemptReconnect: autoReconnect_ is false, aborting" << std::endl;
        return;
    }

    if (lastHost_.empty()) {
        std::cerr << "attemptReconnect: lastHost_ is empty, aborting" << std::endl;
        return;
    }

    // Check if we've exceeded max attempts
    if (reconnectAttempts_ >= maxReconnectAttempts_) {
        std::cerr << "Max reconnection attempts reached. Giving up." << std::endl;
        autoReconnect_ = false;
        if (errorCallback_) {
            errorCallback_("Max reconnection attempts reached");
        }
        return;
    }

    reconnectAttempts_++;

    // Calculate exponential backoff delay: 2, 4, 8, 16, 32, 60, 60, ...
    int delaySeconds = std::min(
        static_cast<int>(initialReconnectDelay_.count() * (1 << (reconnectAttempts_ - 1))),
        maxReconnectDelay_
    );

    std::cerr << "Attempting reconnection " << reconnectAttempts_ << "/" << maxReconnectAttempts_
              << " in " << delaySeconds << " seconds..." << std::endl;

    // Start reconnection in a separate thread to avoid blocking
    if (reconnectThread_ && reconnectThread_->joinable()) {
        reconnectThread_->join();
    }

    reconnectThread_ = std::make_unique<std::thread>([this, delaySeconds]() {
        std::cerr << "Reconnect thread started, delaySeconds=" << delaySeconds << std::endl;

        // Countdown with status updates
        for (int i = delaySeconds; i > 0; i--) {
            if (shouldStop_ || !autoReconnect_) {
                std::cerr << "Reconnect thread stopping early: shouldStop_=" << shouldStop_ << ", autoReconnect_=" << autoReconnect_ << std::endl;
                return;
            }

            std::cerr << "Countdown: " << i << " seconds remaining" << std::endl;

            // Update status with countdown
            if (errorCallback_) {
                errorCallback_("Reconnecting in " + std::to_string(i) + " seconds...");
            }

            std::this_thread::sleep_for(std::chrono::seconds(1));
        }

        std::cerr << "Countdown completed, attempting connection..." << std::endl;

        if (!shouldStop_ && autoReconnect_) {
            // Update status to show connection attempt
            if (errorCallback_) {
                errorCallback_("Connecting...");
            }

            // Reset shouldStop_ flag before attempting reconnection
            {
                std::lock_guard<std::mutex> lock(stateMutex_);
                shouldStop_ = false;
            }

            if (connect(lastHost_, lastPort_)) {
                std::cerr << "Reconnection successful!" << std::endl;
                reconnectAttempts_ = 0; // Reset on successful connection
            } else {
                std::cerr << "Reconnection attempt " << reconnectAttempts_ << " failed." << std::endl;
                // Schedule next attempt
                if (!shouldStop_ && autoReconnect_) {
                    attemptReconnect();
                }
            }
        }
    });
}
