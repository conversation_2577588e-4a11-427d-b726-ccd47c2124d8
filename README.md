# Piano Keyboard

A real-time piano keyboard application with MIDI input support, WebSocket networking, and high-quality audio rendering.

## Features

- **Real-time Piano Playing**: Mouse, keyboard, and MIDI input support
- **High-Quality Audio**: BASS audio engine with soundfont support
- **WebSocket Networking**: Share your piano playing in real-time with other users
- **Cross-Platform**: Supports Linux (NixOS) and Windows
- **MIDI Input**: Multiple MIDI input methods for maximum compatibility
- **Customizable Interface**: Configurable keyboard layout, colors, and effects
- **Visual Effects**: Note indicators, velocity-sensitive colors, and smooth animations

## Supported Platforms

### Linux (NixOS)
- Native support with NixOS development environment
- ALSA MIDI input support
- Optimized for Linux audio systems

### Windows
- Visual Studio (MSVC) and MinGW support
- Windows MIDI API integration
- Native Windows audio support

## Quick Start

### Linux (NixOS)
```bash
# Enter development environment
nix-shell

# Build and run
xmake
xmake run
```

### Windows
```cmd
# Use the provided build script
build_windows.bat

# Or manually:
xmake f -p windows
xmake
xmake run
```

## Requirements

### Common Requirements
- **xmake** build system
- **BASS Audio Library** (bass.dll/.so)
- **BASSMIDI Library** (bassmidi.dll/.so)
- **Soundfont files** (.sf2 format)

### Platform-Specific Requirements

#### Linux
- NixOS with development tools
- ALSA development libraries
- OpenGL and X11 libraries

#### Windows
- Visual Studio 2019+ or MinGW-w64
- Windows SDK
- BASS libraries (download from un4seen.com)

## Building

### Linux (NixOS)
See the main build instructions for NixOS development environment.

### Windows
See `README_Windows.md` for detailed Windows build instructions.

Quick build options:
- `build_windows.bat` - Interactive build script
- `build_windows.ps1` - PowerShell build script
- `package_windows.ps1` - Create distribution package

## WebSocket Networking

The application supports real-time MIDI sharing via WebSocket:

1. **Connect to Server**: Configure host and port in WebSocket settings
2. **Real-time Sharing**: Your piano playing is shared with other connected users
3. **Multi-user Support**: See other users' playing with color-coded notes
4. **Low Latency**: Optimized for real-time musical performance

### WebSocket Features
- Automatic user management
- MIDI event distribution
- Color-coded user identification
- Connection status monitoring
- Error handling and reconnection

## MIDI Input Support

Multiple MIDI input methods for maximum compatibility:

### Linux
- **BASS MIDI**: Cross-platform MIDI input
- **ALSA MIDI**: Native Linux MIDI support
- **External Process**: Custom MIDI applications

### Windows
- **Windows MIDI**: Native Windows MIDI API
- **BASS MIDI**: Cross-platform MIDI input
- **External Process**: Custom MIDI applications

## Audio Features

- **Soundfont Support**: High-quality .sf2 soundfont rendering
- **Low Latency**: Optimized audio pipeline
- **Multiple Devices**: Support for various audio output devices
- **Audio Limiter**: Prevents audio clipping and distortion
- **Real-time Processing**: Immediate response to input

## Configuration

The application automatically saves settings including:
- Audio device and buffer settings
- MIDI input configuration
- Keyboard layout and appearance
- WebSocket connection settings
- Window size and position

## File Locations

### Linux
- Configuration: `~/.config/pianowo/`
- Soundfonts: `./soundfonts/` or custom path

### Windows
- Configuration: `%APPDATA%\pianowo\`
- Soundfonts: `.\soundfonts\` or custom path

## Dependencies

Automatically managed by xmake:
- **GLFW**: Window management and input
- **OpenGL**: Graphics rendering
- **ImGui**: User interface
- **libwebsockets**: WebSocket networking
- **nlohmann_json**: JSON parsing

Manual dependencies:
- **BASS**: Audio engine (licensing restrictions)
- **BASSMIDI**: MIDI soundfont support

## License

[Add your license information here]

## Contributing

[Add contribution guidelines here]

## Support

For platform-specific issues:
- Linux: See main documentation
- Windows: See `README_Windows.md`

For general issues, please check the troubleshooting sections in the platform-specific documentation.
