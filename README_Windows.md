# Piano Keyboard - Windows Build Guide

This guide explains how to build and run the Piano Keyboard application on Windows.

## Prerequisites

### 1. Install xmake
Download and install xmake from: https://xmake.io/#/getting_started

### 2. Install Visual Studio or MinGW
Choose one of the following:

#### Option A: Visual Studio (Recommended)
- Install Visual Studio 2019 or later with C++ development tools
- Make sure to include Windows SDK

#### Option B: MinGW-w64
- Install MinGW-w64 from: https://www.mingw-w64.org/
- Add MinGW bin directory to your PATH

### 3. Download BASS Libraries
Download the following libraries from https://www.un4seen.com/:

1. **BASS** (bass24.zip)
   - Extract `bass.dll` from the archive
   
2. **BASSMIDI** (bassmidi24.zip)
   - Extract `bassmidi.dll` from the archive

Place both DLL files in the project root directory or in the same directory as the built executable.

## Building

### Using Visual Studio (MSVC)
```cmd
# Configure for Windows with MSVC
xmake f -p windows

# Build the project
xmake

# Run the application
xmake run
```

### Using MinGW
```cmd
# Configure for MinGW
xmake f -p mingw

# Build the project
xmake

# Run the application
xmake run
```

## Required Files

After building, make sure the following files are in the same directory as `pianowo.exe`:

1. `bass.dll` - BASS audio library
2. `bassmidi.dll` - BASSMIDI library
3. `GothicA1-Regular.ttf` - Font file (automatically embedded during build)

## Soundfonts

Place your `.sf2` soundfont files in one of these locations:
- Same directory as the executable
- `soundfonts/` subdirectory
- Any custom path (configurable in settings)

## MIDI Input on Windows

The application supports multiple MIDI input methods on Windows:

### 1. Windows MIDI Input (Recommended)
- Uses native Windows MIDI API (winmm)
- Best compatibility with Windows MIDI devices
- Lowest latency

### 2. BASS MIDI Input
- Cross-platform MIDI input
- Good compatibility
- Slightly higher latency

### 3. External Process MIDI
- For custom MIDI applications
- Reads CSV format: `status,data1,data2` (in hex)

## WebSocket Support

The application includes WebSocket support for real-time MIDI sharing:

1. Start a Piano WebSocket server (separate application)
2. Configure host and port in the WebSocket settings
3. Connect to share your piano playing in real-time

## Troubleshooting

### Build Issues

**Error: "Cannot find BASS libraries"**
- Make sure `bass.dll` and `bassmidi.dll` are in the project directory
- The application loads these libraries dynamically at runtime

**Error: "Package not found"**
- Run `xmake repo --update` to update package repository
- Try `xmake f -c` to clean configuration and reconfigure

**Error: "WinSock redefinition" or "sockaddr redefinition"**
- This is a Windows SDK header conflict issue
- The build scripts now automatically clean and reconfigure to fix this
- If the error persists, try: `xmake clean && xmake f -c -p windows && xmake`

**Error: "NOMINMAX macro redefinition"**
- This warning can be safely ignored
- The build system automatically handles Windows macro definitions

### Runtime Issues

**No audio output:**
1. Check that BASS libraries are in the correct location
2. Verify your audio device is working
3. Try different audio devices in the settings

**MIDI input not working:**
1. Make sure your MIDI device is connected and recognized by Windows
2. Try different MIDI input methods (Windows MIDI vs BASS MIDI)
3. Check Device Manager for MIDI device status

**Application crashes on startup:**
- Ensure all required DLL files are present
- Check Windows Event Viewer for detailed error information
- Try running from command prompt to see error messages

## Performance Tips

1. **Audio Settings:**
   - Use ASIO drivers if available for lowest latency
   - Adjust buffer size in audio settings
   - Enable audio limiter to prevent clipping

2. **Graphics Settings:**
   - Enable VSync for smooth rendering
   - Adjust window size for better performance
   - Disable unnecessary visual effects

3. **MIDI Settings:**
   - Use Windows MIDI Input for best performance
   - Close unused MIDI applications
   - Adjust MIDI buffer settings if experiencing dropouts

## File Locations

- **Configuration:** `%APPDATA%\pianowo\config.json`
- **Logs:** Console output (run from command prompt to see)
- **Soundfonts:** Configurable in settings

## Building from Source

If you want to modify the source code:

1. Clone the repository
2. Follow the build instructions above
3. Source files are in the `src/` directory
4. Main configuration is in `xmake.lua`

## Dependencies

The application automatically downloads and builds these dependencies:
- GLFW (windowing)
- OpenGL (graphics)
- ImGui (user interface)
- libwebsockets (WebSocket support)
- nlohmann_json (JSON parsing)

BASS libraries must be provided manually due to licensing restrictions.
